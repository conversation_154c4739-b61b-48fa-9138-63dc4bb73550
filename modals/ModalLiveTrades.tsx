"use client";

import React, { useEffect, useRef, useState } from "react";
import { App<PERSON><PERSON><PERSON>, AppToggle } from "@/components";
import AppModal from "./AppModal";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../store/index";
import { setWalletTracker } from "@/store/user.store";

export const ModalLiveTrades = ({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
}) => {
  const [isEnableToast, setIsEnableToast] = useState<boolean>(false);
  // const [isEnablePauseLive, setIsEnablePauseLive] = useState<boolean>(false);
  const [isEnableSound, setIsEnableSound] = useState<boolean>(false);
  // const [toastDuration, setToastDuration] = useState(5);
  const [volume, setVolume] = useState(80);
  const [isTooltipVisible, setIsTooltipVisible] = useState(false);
  const [tooltipLeft, setTooltipLeft] = useState(0);
  const walletTracker = useSelector(
    (state: RootState) => state.user.walletTracker
  );
  const dispatch = useDispatch<AppDispatch>();

  // const toastRef = useRef<HTMLInputElement>(null);
  const volumeRef = useRef<HTMLInputElement>(null);

  const updateSliderProgress = (
    ref: React.RefObject<HTMLInputElement | null>,
    rawValue: number,
    min: number,
    max: number
  ) => {
    if (!ref.current) return;
    const percent = ((rawValue - min) / (max - min)) * 100;
    ref.current.style.setProperty("--range-progress", `${percent}%`);
  };

  // useEffect(() => {
  //   updateSliderProgress(toastRef, toastDuration, 1, 10);
  // }, [toastDuration]);

  useEffect(() => {
    updateSliderProgress(volumeRef, volume, 0, 100);
  }, [volume]);

  const handleVolumeMouseMove = (e: React.MouseEvent<HTMLInputElement>) => {
    if (!volumeRef.current) return;

    const rect = volumeRef.current.getBoundingClientRect();
    const left =
      ((volume - Number(volumeRef.current.min)) /
        (Number(volumeRef.current.max) - Number(volumeRef.current.min))) *
      rect.width;

    setTooltipLeft(left);
  };

  const handleVolumeMouseEnter = () => {
    setIsTooltipVisible(true);
  };

  const handleVolumeMouseLeave = () => {
    setIsTooltipVisible(false);
  };

  useEffect(() => {
    setIsEnableSound(walletTracker?.settings?.isEnabledSound);
    setIsEnableToast(walletTracker?.settings?.isEnabledToast);
    setVolume(walletTracker?.settings?.soundVolume);
  }, [walletTracker]);

  const onSave = () => {
    dispatch(
      setWalletTracker({
        walletTracker: {
          ...walletTracker,
          settings: {
            isEnableSound,
            isEnableToast,
            soundVolume: +volume,
          },
        },
      })
    );
    onClose();
  };

  return (
    <AppModal
      isOpen={isOpen}
      title="Live Trades Settings"
      onClose={onClose}
      className="w-[343px] max-w-[375px]"
    >
      <div className="flex flex-col gap-3">
        <div className="my-[16px] flex items-center justify-between">
          <div className="body-md-regular-14 text-white-500">
            Open toast alerts in new tab
          </div>
          <AppToggle
            value={isEnableToast}
            onChange={() => setIsEnableToast(!isEnableToast)}
          />
        </div>
        {/*<div className="flex items-center justify-between mb-[16px]">*/}
        {/*  <div className="body-md-regular-14 text-white-500">*/}
        {/*    Pause live feed on hover*/}
        {/*  </div>*/}
        {/*  <AppToggle*/}
        {/*    value={isEnablePauseLive}*/}
        {/*    onChange={() => setIsEnablePauseLive(!isEnablePauseLive)}*/}
        {/*  />*/}
        {/*</div>*/}

        {/*<div className="flex items-center justify-between mb-[16px]">*/}
        {/*  <div className="body-md-regular-14 text-white-500">*/}
        {/*    Toast duration*/}
        {/*  </div>*/}
        {/*  <div className="body-sm-medium-12 text-white-1000 border-white-100 w-[112px] rounded-[6px] border p-2 text-center">*/}
        {/*    {toastDuration} S*/}
        {/*  </div>*/}
        {/*</div>*/}

        {/*<div>*/}
        {/*  <input*/}
        {/*    type="range"*/}
        {/*    min={1}*/}
        {/*    max={10}*/}
        {/*    step={1}*/}
        {/*    value={toastDuration}*/}
        {/*    ref={toastRef}*/}
        {/*    onInput={(e) => {*/}
        {/*      const val = Number(e.currentTarget.value);*/}
        {/*      setToastDuration(val);*/}
        {/*      updateSliderProgress(toastRef, val, 1, 10);*/}
        {/*    }}*/}
        {/*    className="slider-custom"*/}
        {/*  />*/}
        {/*  <div className="body-xs-regular-10 text-white-500 flex items-center justify-between">*/}
        {/*    <div>1</div>*/}
        {/*    <div>10</div>*/}
        {/*  </div>*/}
        {/*</div>*/}

        <div className="pt-4">
          <div className="mb-4 flex items-center justify-between">
            <div className="body-md-regular-14 text-white-500">
              Sound alerts
            </div>
            <AppToggle
              value={isEnableSound}
              onChange={() => setIsEnableSound(!isEnableSound)}
            />
          </div>
          <div className="body-sm-regular-12 text-white-500">
            Play sound alerts for alerted wallets
          </div>
        </div>

        <div className="flex items-center justify-between py-[16px]">
          <div className="body-md-regular-14 text-white-500">Volume</div>
          <div className="body-sm-medium-12 text-white-1000 border-white-100 w-[112px] rounded-[6px] border p-2 text-center">
            {volume} %
          </div>
        </div>

        <div className="relative mb-[32px]">
          <input
            type="range"
            min={0}
            max={100}
            value={volume}
            onChange={(e) => setVolume(Number(e.target.value))}
            onMouseMove={handleVolumeMouseMove}
            onMouseEnter={handleVolumeMouseEnter}
            onMouseLeave={handleVolumeMouseLeave}
            className="slider-custom"
            ref={volumeRef}
          />

          {isTooltipVisible && (
            <div className="tooltip-volume" style={{ left: tooltipLeft }}>
              {volume}%
            </div>
          )}

          <div className="body-xs-regular-10 text-white-500 flex items-center justify-between">
            <div>0%</div>
            <div>25%</div>
            <div>50%</div>
            <div>75%</div>
            <div>100%</div>
          </div>
        </div>
      </div>
      <AppButton
        onClick={onSave}
        variant="buy"
        size="large"
        className="!action-md-medium-14"
      >
        Done
      </AppButton>
    </AppModal>
  );
};
