import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { setAuthorizationToRequest } from "@/utils/authenticate";
import Storage from "@/libs/storage";
import { BROADCAST_EVENTS, AppBroadcast } from "@/libs/broadcast";
import { JwtPayload, jwtDecode } from "jwt-decode";
import moment from "moment";
import { NETWORKS } from "@/utils/contants";
import { TOrder, TPair, TPosition, TWallet } from "@/types";
import rf from "@/services/RequestFactory";
import { TBalance, TBalanceOnchain } from "@/types/balance.type";
import BigNumber from "bignumber.js";
import { setIsShowModalGetStart } from "@/store/metadata.store";
import { overridePairStatIfNeed } from "@/utils/pair";
import _ from "lodash";
import { normalizeStructTag, normalizeSuiAddress } from "@mysten/sui/utils";
import { TWalletTracker } from "../types/wallet-tracker";

export type UserState = {
  accessToken: string;
  referralMyCode: string;
  network: string;
  userId: string;
  wallets: TWallet[];
  balances: TBalance[];
  balancesOnchain: TBalanceOnchain[];
  positions: TPosition[];
  openOrders: TOrder[];
  settingsOrder: any;
  favouritePairs: TPair[];
  permissions: any[];
  isExternalWallet: boolean;
  walletTracker: {
    settings: {
      isEnabledToast: boolean;
      isEnabledSound: boolean;
      soundVolume: number;
    };
    wallets: TWalletTracker[];
  };
};

const isValidAccessToken = (accessToken: string | undefined) => {
  if (!accessToken) return false;

  let decodedInfo: JwtPayload;

  try {
    decodedInfo = jwtDecode(accessToken);
  } catch (e) {
    console.log("jwt decode error", e);
    return false;
  }

  const currentTime = moment().unix();

  if (!decodedInfo.exp || +decodedInfo.exp < currentTime) {
    console.log("The user JWT is expired");
    return false;
  }

  return true;
};

export const getValidAccessTokenFromStorage = () => {
  const accessToken = Storage.getAccessToken();
  if (!accessToken) return "";

  const isValid = isValidAccessToken(accessToken);

  if (!isValid) {
    Storage.clearAccessToken();
    AppBroadcast.dispatch(BROADCAST_EVENTS.LOGOUT, {});
    return "";
  }

  return accessToken || "";
};

export const getFavouritePairs = createAsyncThunk(
  "user/getFavouritePairs",
  async (_, thunkApi) => {
    const res = await rf.getRequest("FavouriteRequest").getPairs(NETWORKS.SUI, {
      page: 1,
      limit: 200,
    });
    const newData = res?.docs || [];

    const favouritePairIds = Storage.getFavourites(NETWORKS.SUI);
    if (!newData?.length && favouritePairIds?.length > 0) {
      const res =
        (await rf.getRequest("PairRequest").getPairRecently(NETWORKS.SUI, {
          page: 1,
          limit: 1000,
          timestamp: 0,
          pairIds: favouritePairIds?.join(","),
        })) || {};
      const pairs = res.docs || [];
      await Promise.all(
        pairs?.map((pair: TPair) =>
          rf.getRequest("FavouriteRequest").favourite(NETWORKS.SUI, pair.slug)
        )
      );
    } else if (newData?.length > 0) {
      favouritePairIds?.forEach((id) =>
        Storage.removeFavourite(NETWORKS.SUI, id)
      );
      newData?.map((pair: TPair) =>
        Storage.addFavourite(NETWORKS.SUI, pair.pairId)
      );
    }
    thunkApi.dispatch(
      setFavouritePairs(
        newData?.map((item: TPair) => {
          return {
            ...item,
            stats: overridePairStatIfNeed(item.stats),
          };
        })
      )
    );
  }
);

export const getWalletsUser = createAsyncThunk(
  "user/getWalletsUser",
  async ({ network }: { network: string }, thunkApi) => {
    let wallets = await rf.getRequest("WalletRequest").getWallets(network);
    if (wallets?.length) {
      wallets = wallets.map((item: TWallet) => {
        return {
          ...item,
          balance: +item.balance,
        };
      });
      wallets = _.orderBy(wallets, ["balance"], ["desc"]);
    }

    thunkApi.dispatch(setWallets({ wallets }));
  }
);

export const getAllWalletAndBalanceUser = createAsyncThunk(
  "user/getAllWalletAndBalanceUser",
  async (
    { network, isFirstTime }: { network: string; isFirstTime?: boolean },
    thunkApi
  ) => {
    let wallets = await rf.getRequest("WalletRequest").getWallets(network);
    if (!wallets.length && isFirstTime) {
      thunkApi.dispatch(setIsShowModalGetStart({ isShow: true }));
    }

    if (wallets.length) {
      wallets = wallets.map((item: TWallet) => {
        return {
          ...item,
          balance: +item.balance,
        };
      });

      wallets = _.orderBy(wallets, ["balance"], ["desc"]);
    }

    thunkApi.dispatch(setWallets({ wallets }));

    for (const wallet of wallets) {
      const res = await rf
        .getRequest("HolderRequest")
        .getMyBalances(wallet.network, {
          walletAddress: wallet.address,
          limit: 200,
        });
      res.docs?.map((balance: TBalance) => {
        thunkApi.dispatch(setBalance(balance));
      });
    }
  }
);

export const getReferralMyCode = createAsyncThunk(
  "user/getReferralMyCode",
  async (_, thunkApi) => {
    const res = await rf.getRequest("ReferralRequest").getReferralMetadata();
    if (!!res) {
      thunkApi.dispatch(setReferralMyCode(res.myReferralCode));
    }
  }
);

export const getSettingsOrder = createAsyncThunk(
  "user/getSettingsOrder",
  async ({ network }: { network: string }, thunkApi) => {
    const data = await rf
      .getRequest("PresetSettingRequest")
      .getTradeSettings(network);
    thunkApi.dispatch(setSettingsOrder({ settingsOrder: data }));
  }
);

export const getGroupsWalletTracker = createAsyncThunk(
  "user/getGroupsWalletTracker",
  async ({ network }: { network: string }, thunkApi) => {
    const data = await rf.getRequest("WalletTrackerRequest").getGroups();
    thunkApi.dispatch(setWalletTracker({ settingsOrder: data }));
  }
);

const initialState: UserState = {
  accessToken: getValidAccessTokenFromStorage(),
  userId: "",
  referralMyCode: "",
  network: NETWORKS.SUI,
  wallets: [],
  positions: [],
  balances: [],
  balancesOnchain: [],
  openOrders: [],
  settingsOrder: {},
  favouritePairs: [],
  permissions: [],
  isExternalWallet: false,
  walletTracker: {
    settings: {
      isEnabledToast: true,
      isEnabledSound: true,
      soundVolume: 50,
    },
    wallets: [],
  },
};

const userSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    setUserAuth: (state, action) => {
      const { accessToken } = action.payload;

      Storage.setAccessToken(accessToken);
      setAuthorizationToRequest(accessToken);

      const decodeInfo = jwtDecode(accessToken) as any;
      state.userId = decodeInfo?.userId;

      state.accessToken = accessToken;

      // Clear referral code when user 1st login
      if (Storage.getReferralCode()) {
        Storage.setReferralCode("");
      }
    },

    setPosition: (state, action) => {
      const newPosition = action.payload;
      const existingIndex = state.positions.findIndex(
        (position) =>
          normalizeStructTag(position.token.address) ===
            normalizeStructTag(newPosition.token.address) &&
          normalizeSuiAddress(position.walletName) ===
            normalizeSuiAddress(newPosition.walletName)
      );
      if (existingIndex >= 0) {
        state.positions[existingIndex] = newPosition;
      } else {
        state.positions.push(newPosition);
      }
      state.positions = state.positions.filter((position) =>
        new BigNumber(position.balance).isGreaterThan(0)
      );
    },

    removePosition: (state, action) => {
      const deletePosition = action.payload;
      const positions = [...state.positions];
      const newPositions = positions.filter(
        (position) =>
          !(
            normalizeStructTag(position.token.address) ===
              normalizeStructTag(deletePosition.token.address) &&
            normalizeSuiAddress(position.walletName) ===
              normalizeSuiAddress(deletePosition.walletName)
          )
      );
      state.positions = newPositions;
    },

    setReferralMyCode: (state, action) => {
      state.referralMyCode = action.payload;
    },

    setFavouritePairs: (state, action) => {
      state.favouritePairs = action.payload;
    },

    setWallets: (state, action) => {
      const { wallets } = action.payload;
      state.wallets = wallets;
    },

    setWalletTracker: (state, action) => {
      const { walletTracker } = action.payload;
      state.walletTracker = walletTracker;
    },

    setOpenOrders: (state, action) => {
      state.openOrders = action.payload;
    },

    setSettingsOrder: (state, action) => {
      const { settingsOrder } = action.payload;
      state.settingsOrder = settingsOrder;
    },

    setBalance: (state, action) => {
      const newBalance: TBalance = action.payload;
      const existingIndex = state.balances?.findIndex(
        (balance) =>
          balance?.token?.address &&
          normalizeStructTag(balance?.token?.address) ===
            normalizeStructTag(newBalance?.token?.address) &&
          normalizeSuiAddress(balance?.walletAddress) ===
            normalizeSuiAddress(newBalance?.walletAddress)
      );
      if (existingIndex >= 0) {
        state.balances[existingIndex] = {
          ...state.balances[existingIndex],
          ...newBalance,
        };
      } else {
        state.balances.push(newBalance);
      }
    },

    setPermissions: (state, action) => {
      state.permissions = action.payload;
    },

    setIsExternalWallet: (state, action) => {
      state.isExternalWallet = action.payload;
    },

    clearUser: () => {
      setAuthorizationToRequest(null);
      Storage.clearAccessToken();
      Storage.clearHistorySearch();
      Storage.clearWalletAddresses();
      return {
        accessToken: "",
        referralMyCode: "",
        network: NETWORKS.SUI,
        wallets: [],
        positions: [],
        balances: [],
        balancesOnchain: [],
        openOrders: [],
        userId: "",
        settingsOrder: {},
        favouritePairs: [],
        permissions: [],
        isExternalWallet: false,
        walletTracker: {
          settings: {
            isEnabledToast: true,
            isEnabledSound: true,
            soundVolume: 50,
          },
          wallets: [],
        },
      };
    },
  },
});

export const {
  setUserAuth,
  clearUser,
  setWallets,
  setSettingsOrder,
  setReferralMyCode,
  setBalance,
  setOpenOrders,
  setFavouritePairs,
  removePosition,
  setPosition,
  setPermissions,
  setIsExternalWallet,
  setWalletTracker,
} = userSlice.actions;

export default userSlice.reducer;
