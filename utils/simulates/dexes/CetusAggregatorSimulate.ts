import BaseSimulate from "../BaseSimulate";
import { CoinStruct, getFullnodeUrl, SuiClient } from "@mysten/sui/client";
import BigNumber from "bignumber.js";
import { TCoinMetadata, TPosition } from "@/types";
import { convertDecToMist, isZero, toStringBN } from "@/utils/helper";
import {
  getOwnerCoinOnchain,
  getReferenceGasPrice,
  suiClient,
} from "@/utils/suiClient";
import { normalizeStructTag, normalizeSuiAddress } from "@mysten/sui/utils";
import { AggregatorClient } from "@cetusprotocol/aggregator-sdk";
import { Transaction } from "@mysten/sui/transactions";
import config from "@/config";

export default class CetusAggregatorSimulate extends BaseSimulate {
  public buildBuyTransaction = async (
    walletAddress: string,
    exactAmountIn: BigNumber | string | number,
    tokenOut: TCoinMetadata,
    tokenIn: TCoinMetadata
  ) => {
    const client = new AggregatorClient({
      signer: walletAddress,
      overlayFeeRate: config.raidenxFeeRate,
      overlayFeeReceiver: config.raidenxAddress,
    });
    const amount = new BigNumber(toStringBN(exactAmountIn));
    const from = tokenIn.address;
    const target = tokenOut.address;

    const routers = await client.findRouters({
      from,
      target,
      amount,
      byAmountIn: true, // true means fix input amount, false means fix output amount
    });

    console.log("routers", routers);

    if (!routers) {
      throw new Error(
        `No routes found for tokenIn: ${from} and tokenOut: ${target}`
      );
    }

    const txb = new Transaction();
    await client.fastRouterSwap({
      router: routers,
      txb,
      slippage: 1,
    });

    txb.setSender(walletAddress);
    return { tx: txb, amountOut: routers.amountOut?.toString() };
  };

  private buildSellTransaction = async (
    walletAddress: string,
    exactAmountIn: BigNumber,
    tokenIn: TCoinMetadata,
    tokenOut: TCoinMetadata,
    coinObjs: (CoinStruct & { owner: string })[]
  ) => {
    const client = new AggregatorClient({
      signer: walletAddress,
      overlayFeeRate: config.raidenxFeeRate,
      overlayFeeReceiver: config.raidenxAddress,
    });
    const amount = new BigNumber(toStringBN(exactAmountIn));
    const from = tokenIn.address;
    const target = tokenOut.address;

    const routers = await client.findRouters({
      from,
      target,
      amount,
      byAmountIn: true,
    });

    if (!routers) {
      throw new Error(
        `No routes found for tokenIn: ${from} and tokenOut: ${target}`
      );
    }

    const txb = new Transaction();
    await client.fastRouterSwap({
      router: routers,
      txb,
      slippage: 1,
    });
    txb.setSender(walletAddress);

    return { tx: txb, amountOut: routers.amountOut?.toString() };
  };

  public extractBaseTokenOut = async (
    walletAddress: string,
    amountIn: string,
    tokenIn: TCoinMetadata,
    tokenOut: TCoinMetadata,
    poolObjectId: string | undefined
  ) => {
    return this.buildBuyTransaction(walletAddress, amountIn, tokenOut, tokenIn);
  };

  public extractQuoteTokenOut = async (
    walletAddress: string,
    tokenIn: TCoinMetadata,
    tokenOut: TCoinMetadata,
    sellPercent: number,
    poolObjectId: string | undefined,
    gasBasePrice: bigint
  ) => {
    if (!poolObjectId) {
      throw new Error("Pool object id not found");
    }

    const [coinObjs, currentAmount] = await getOwnerCoinOnchain(
      walletAddress,
      tokenIn.address
    );

    if (currentAmount.isZero()) {
      throw new Error("Insufficient balance");
    }

    const exactAmountIn = currentAmount
      .multipliedBy(sellPercent)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    return this.buildSellTransaction(
      walletAddress,
      exactAmountIn,
      tokenIn,
      tokenOut,
      coinObjs
    );
  };

  public extractQuoteTokenOutPositionWithSponsor = async (
    position: TPosition,
    userAllCoins: (CoinStruct & { owner: string })[],
    gasBasePrice?: bigint
  ) => {
    const tokenCoinObjects = userAllCoins.filter(
      (coin) =>
        normalizeStructTag(coin.coinType) ===
          normalizeStructTag(position.token.address) &&
        normalizeSuiAddress(coin.owner) ===
          normalizeSuiAddress(position.walletName)
    );
    if (!tokenCoinObjects) {
      throw new Error("Token not found");
    }
    const tokenBalance = tokenCoinObjects.reduce(
      (prev, coin) => prev.plus(coin.balance),
      new BigNumber(0)
    );

    if (!gasBasePrice) {
      gasBasePrice = await getReferenceGasPrice();
    }

    if (isZero(tokenBalance)) {
      throw new Error("Insufficient balance");
    }

    const exactAmountIn = tokenBalance
      .multipliedBy(100)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    const result = await this.buildSellTransaction(
      position.walletName,
      exactAmountIn,
      position.token,
      position.tokenQuote,
      tokenCoinObjects
    );
    result.tx.setSender(position.walletName);
    return {
      tx: await this.buildSponsoredTransaction(result.tx),
      amountOut: result.amountOut || NaN,
    };
  };

  public getNameInstance() {
    return "CetusAggregatorSimulate";
  }
}
